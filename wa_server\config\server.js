/**
 * Optimized Multi-Tenant WhatsApp Server
 * Modular architecture for better maintainability and performance
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const url = require('url');

// Load environment variables if .env exists
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
    require('dotenv').config({ path: envPath });
}

// Custom modules
const config = require('./config');
const WhatsAppClientManager = require('./client-manager');
const ApiService = require('./api-service');

class WhatsAppServer {
    constructor() {
        this.server = null;
        this.clientManager = null;
        this.apiService = null;
        this.isShuttingDown = false;
    }

    async initialize() {
        try {
            // Initialize services
            this.apiService = new ApiService();
            this.clientManager = new WhatsAppClientManager(this.apiService);

            // Create HTTP server with API endpoints
            this.server = http.createServer((req, res) => {
                this.handleRequest(req, res);
            });

            // Setup graceful shutdown
            this.setupGracefulShutdown();

            console.log('✅ Server initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize server:', error);
            throw error;
        }
    }

    async handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        const path = parsedUrl.pathname;
        const method = req.method;

        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        if (method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }

        try {
            if (path === '/check-connection' && method === 'POST') {
                await this.handleCheckConnection(req, res);
            } else if (path === '/send-message' && method === 'POST') {
                await this.handleSendMessage(req, res);
            } else if (path === '/logout' && method === 'POST') {
                await this.handleLogout(req, res);
            } else if (path === '/heartbeat' && method === 'POST') {
                await this.handleHeartbeat(req, res);
            } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Not found' }));
            }
        } catch (error) {
            console.error('❌ Request handling error:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Internal server error' }));
        }
    }



    async getRequestBody(req) {
        return new Promise((resolve, reject) => {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });
            req.on('end', () => {
                try {
                    resolve(body ? JSON.parse(body) : {});
                } catch (error) {
                    reject(new Error('Invalid JSON'));
                }
            });
            req.on('error', reject);
        });
    }

    async handleCheckConnection(req, res) {
        try {
            const { userId } = await this.getRequestBody(req);

            if (!userId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'User ID required' }));
                return;
            }

            console.log(`🔍 Checking WhatsApp connection for user ${userId}`);

            // Check if user already exists in sessions
            if (this.clientManager.clients.has(userId)) {
                const currentStatus = this.clientManager.getClientState(userId);
                console.log(`♻️ User ${userId} already has an active session (status: ${currentStatus?.status})`);

                // If client is in a good state, return it
                if (currentStatus && (currentStatus.status === 'connected' || currentStatus.status === 'ready')) {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        success: true,
                        message: 'Existing WhatsApp session found',
                        status: currentStatus,
                        isExisting: true
                    }));
                    return;
                } else {
                    // Client exists but is in bad state, let it fall through to create new one
                    console.log(`🔄 Existing client for user ${userId} is in bad state, will create new session`);
                }
            }

            // User doesn't exist or needs new session, create it
            console.log(`🆕 Creating new WhatsApp session for user ${userId}`);
            await this.clientManager.getClient(userId);
            const newStatus = this.clientManager.getClientState(userId);

            // Update heartbeat to mark as active
            this.clientManager.updateClientState(userId, {
                lastActivity: Date.now()
            });

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                message: 'New WhatsApp session created',
                status: newStatus,
                isExisting: false
            }));

        } catch (error) {
            console.error(`❌ WhatsApp connection error:`, error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: error.message }));
        }
    }



    async handleLogout(req, res) {
        try {
            const { userId } = await this.getRequestBody(req);

            if (!userId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'User ID required' }));
                return;
            }

            console.log(`🚪 Logging out WhatsApp session for user ${userId}`);

            const client = this.clientManager.clients.get(userId);
            if (client) {
                try {
                    await client.destroy();
                    this.clientManager.clients.delete(userId);
                    this.clientManager.clientStates.delete(userId);
                    await this.clientManager.cleanupSessionFiles(userId);

                    // Notify Laravel about logout status
                    this.apiService.notifyAuth(userId, 'connecting', 'Session logged out - ready for new connection');

                    setTimeout(async () => {
                        try {
                            await this.clientManager.getClient(userId);
                        } catch (error) {
                            console.error(`❌ Error creating new client:`, error);
                        }
                    }, 3000);

                } catch (error) {
                    console.error(`❌ Error during logout:`, error);
                    this.clientManager.clients.delete(userId);
                    this.clientManager.clientStates.delete(userId);
                    await this.clientManager.cleanupSessionFiles(userId);

                    // Notify Laravel about logout status even on error
                    this.apiService.notifyAuth(userId, 'connecting', 'Session logged out after error - ready for new connection');
                }
            } else {
                // No client found, but still notify about logout
                console.log(`ℹ️ No active client found for user ${userId}, but notifying logout status`);
                this.apiService.notifyAuth(userId, 'connecting', 'No active session found - ready for new connection');
            }

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                message: 'WhatsApp session logged out'
            }));

        } catch (error) {
            console.error('WhatsApp logout error:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: error.message }));
        }
    }

    async handleHeartbeat(req, res) {
        try {
            const { userId } = await this.getRequestBody(req);

            if (!userId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'User ID required' }));
                return;
            }

            // Update last activity if client exists
            if (this.clientManager.clientStates.has(userId)) {
                this.clientManager.updateClientState(userId, {
                    lastActivity: Date.now()
                });
            }

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));

        } catch (error) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: error.message }));
        }
    }

    async handleSendMessage(req, res) {
        try {
            const { userId, chatId, message, type = 'text', media } = await this.getRequestBody(req);

            if (!userId || !chatId || !message) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'User ID, chat ID, and message are required' }));
                return;
            }

            const result = await this.clientManager.sendMessage(userId, chatId, message, type, media);

            if (result.success) {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(result));
            } else {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(result));
            }

        } catch (error) {
            console.error('❌ Error sending message:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: error.message }));
        }
    }



    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            if (this.isShuttingDown) {
                console.log('⚠️ Force shutdown initiated');
                process.exit(1);
            }

            this.isShuttingDown = true;
            console.log(`📴 Received ${signal}. Starting graceful shutdown...`);

            try {
                // Stop accepting new connections
                this.server.close(() => {
                    console.log('🔌 HTTP server closed');
                });

                // Disconnect all WhatsApp clients
                console.log('🧹 Disconnecting all WhatsApp clients...');
                const disconnectPromises = Array.from(this.clientManager.clients.keys())
                    .map(userId => this.clientManager.disconnectClient(userId));

                await Promise.allSettled(disconnectPromises);
                console.log('✅ All clients disconnected');

                console.log('✅ Graceful shutdown completed');
                process.exit(0);

            } catch (error) {
                console.error('❌ Error during shutdown:', error);
                process.exit(1);
            }
        };

        // Handle different shutdown signals
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error('💥 Uncaught Exception:', error);
            shutdown('uncaughtException');
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
            shutdown('unhandledRejection');
        });
    }

    async start() {
        try {
            await this.initialize();

            const port = config.server.port;

            this.server.listen(port, async () => {


                // Show existing sessions after server starts
                setTimeout(async () => {
                    await this.showExistingSessions();
                }, 1000); // Wait 1 second for server to fully start
            });

        } catch (error) {
            console.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }

    // Show existing WhatsApp sessions on startup
    async showExistingSessions() {
        console.log('🔍 Scanning for existing WhatsApp sessions...');

        try {
            const sessionsDir = config.paths.sessions;
            if (!fs.existsSync(sessionsDir)) {
                console.log('📁 No sessions directory found');
                return;
            }

            const sessionFolders = fs.readdirSync(sessionsDir)
                .filter(folder => {
                    const folderPath = path.join(sessionsDir, folder);
                    return fs.statSync(folderPath).isDirectory() && folder.startsWith('user_');
                });

            if (sessionFolders.length === 0) {
                console.log('📭 No existing sessions found');
                return;
            }

            console.log(`📊 Found ${sessionFolders.length} existing session(s):`);

            // Display each session with details
            for (const folder of sessionFolders) {
                const userId = folder.replace('user_', '');
                const sessionPath = path.join(sessionsDir, folder);

                try {
                    // Get session folder stats
                    const stats = fs.statSync(sessionPath);
                    const lastModified = stats.mtime.toLocaleString();

                    // Check if session has authentication data
                    const hasAuthData = this.checkSessionAuthData(sessionPath);

                    console.log(`  👤 User ID: ${userId}`);
                    console.log(`     📁 Path: ${sessionPath}`);
                    console.log(`     📅 Last Modified: ${lastModified}`);
                    console.log(`     🔐 Auth Data: ${hasAuthData ? '✅ Present' : '❌ Missing'}`);
                    console.log('     ─────────────────────────────────────');

                } catch (error) {
                    console.log(`  👤 User ID: ${userId} - ❌ Error reading session: ${error.message}`);
                }
            }

            console.log(`✅ Session scan completed - ${sessionFolders.length} session(s) found`);

        } catch (error) {
            console.error('❌ Error scanning sessions:', error.message);
        }
    }

    // Check if session has authentication data
    checkSessionAuthData(sessionPath) {
        try {
            // Check for common WhatsApp session files
            const authFiles = [
                'Default/Local Storage/leveldb',
                'Default/IndexedDB',
                'Default/Session Storage',
                'session.json'
            ];

            for (const authFile of authFiles) {
                const authPath = path.join(sessionPath, authFile);
                if (fs.existsSync(authPath)) {
                    return true;
                }
            }

            // Check if there are any files in the session directory
            const files = fs.readdirSync(sessionPath);
            return files.length > 0;

        } catch (error) {
            return false;
        }
    }

    // Method to get server instance for testing
    getServer() {
        return this.server;
    }

    // Method to get API service
    getApiService() {
        return this.apiService;
    }

    // Method to get client manager
    getClientManager() {
        return this.clientManager;
    }
}

// Start server if this file is run directly
if (require.main === module) {
    const server = new WhatsAppServer();
    server.start().catch(error => {
        console.error('💥 Server startup failed:', error);
        process.exit(1);
    });
}

module.exports = WhatsAppServer;
